﻿<Application x:Class="XChrome.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:XChrome"
             xmlns:adonisUi="clr-namespace:AdonisUI;assembly=AdonisUI"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/AdonisUI;component/ColorSchemes/Light.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/AdonisUI.ClassicTheme;component/Resources.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/ToastNotifications.Messages;component/Themes/Default.xaml" />
            </ResourceDictionary.MergedDictionaries>


            <!-- Override colors as you like -->
            <!--Color x:Key="{x:Static adonisUi:Colors.AccentColor}">#0BAC08</Color-->

        </ResourceDictionary>
    </Application.Resources>
</Application>

