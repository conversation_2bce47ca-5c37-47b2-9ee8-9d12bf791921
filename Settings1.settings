﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="XChrome" GeneratedClassName="Settings1">
  <Profiles />
  <Settings>
    <Setting Name="WindowWidth" Type="System.Double" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="WindowHeight" Type="System.Double" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="WindowTop" Type="System.Double" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="WindowLeft" Type="System.Double" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="WindowState" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="WindowScheme" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
  </Settings>
</SettingsFile>