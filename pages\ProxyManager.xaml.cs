﻿#region copyright
/**
// --------------------------------------------------------------------------------
// 文件名：ProxyManager.xaml.cs
// 作者：刹那 https://x.com/chanawudi
// 公司：https://x.com/chanawudi
// 更新日期：2025，2，27，13:56
// 版权所有 © Your Company. 保留所有权利。
// --------------------------------------------------------------------------------
*/
#endregion
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace XChrome.pages
{
    /// <summary>
    /// ProxyManager.xaml 的交互逻辑
    /// </summary>
    public partial class ProxyManager : Page
    {
        public ProxyManager()
        {
            InitializeComponent();
        }

       
    }
}
