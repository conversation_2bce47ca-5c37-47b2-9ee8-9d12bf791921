﻿<adonisControls:AdonisWindow
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:adonisUi="clr-namespace:AdonisUI;assembly=AdonisUI"
    xmlns:adonisControls="clr-namespace:AdonisUI.Controls;assembly=AdonisUI"
    xmlns:av="http://schemas.microsoft.com/expression/blend/2008" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="av"
    x:Class="XChrome.forms.EditGroup"
    Title="-"
    Width="518"
    Height="304"
    IconVisibility="Hidden"
    TitleVisibility="Hidden"
    ShrinkTitleBarWhenMaximized="False" Loaded="AdonisWindow_Loaded"                           
    PlaceTitleBarOverContent="True" WindowStartupLocation="CenterOwner">
    <Grid Margin="0,0,0,-5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="21*"/>
            <ColumnDefinition Width="16*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="20"/>
            <RowDefinition Height="40"/>
            <RowDefinition Height="50"/>
            <RowDefinition Height="40"/>
            <RowDefinition Height="90"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>

        <Button x:Name="okbtn" x:FieldModifier="private"  Content="确定" HorizontalAlignment="Right" Margin="0,4,6,0" Grid.Row="5" VerticalAlignment="Top" Height="33" Width="80" Style="{DynamicResource {x:Static adonisUi:Styles.AccentButton}}" Grid.Column="1" Click="okbtn_Click"/>
        <Button x:Name="quxiao" x:FieldModifier="private" Content="取消" HorizontalAlignment="Left" Margin="7,5,0,0" Grid.Row="5" VerticalAlignment="Top" Height="33" Width="63" Click="quxiao_Click"/>

        <Label x:Name="tip" x:FieldModifier="private" Content="分组名称：" HorizontalAlignment="Left" Margin="10,7,0,0" Grid.Row="1" VerticalAlignment="Top" Height="30" Width="498" FontSize="18" Grid.ColumnSpan="2"/>
        <TextBox x:Name="content" x:FieldModifier="private" HorizontalAlignment="Left" Margin="10,10,0,0" Grid.Row="2" TextWrapping="Wrap" Text="222" VerticalContentAlignment="Top" VerticalAlignment="Top" Height="29" Width="498" Grid.ColumnSpan="2" FontSize="16" />
        <Label x:Name="tip2" x:FieldModifier="private" Content="备注：" HorizontalAlignment="Left" Margin="10,7,0,0" Grid.Row="3" VerticalAlignment="Top" Height="30" Width="498" FontSize="18" Grid.ColumnSpan="2"/>
        <TextBox x:Name="content2" x:FieldModifier="private" AcceptsReturn="True" HorizontalAlignment="Left" Margin="10,0,0,0" Grid.Row="4" TextWrapping="Wrap" Text="222" VerticalContentAlignment="Top" VerticalAlignment="Center" Height="60" Width="498" Grid.ColumnSpan="2" FontSize="16" />

    </Grid>
</adonisControls:AdonisWindow>
