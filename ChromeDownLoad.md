﻿# Chromium 历史版本离线安装包 - 下载方法

下载地址（非官网，但文件地址是官方的）：https://vikyd.github.io/download-chromium-history-version/

# 目录

<!-- START doctoc generated TOC please keep comment here to allow auto update -->
<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->

- [概述](#%E6%A6%82%E8%BF%B0)
- [Chrome 离线包 - 最新版（官方）](#chrome-%E7%A6%BB%E7%BA%BF%E5%8C%85---%E6%9C%80%E6%96%B0%E7%89%88%E5%AE%98%E6%96%B9)
- [Chrome 离线包 - 历史版本（官方）](#chrome-%E7%A6%BB%E7%BA%BF%E5%8C%85---%E5%8E%86%E5%8F%B2%E7%89%88%E6%9C%AC%E5%AE%98%E6%96%B9)
- [Chrome 离线包 - 历史版本（非官方）](#chrome-%E7%A6%BB%E7%BA%BF%E5%8C%85---%E5%8E%86%E5%8F%B2%E7%89%88%E6%9C%AC%E9%9D%9E%E5%AE%98%E6%96%B9)

<!-- END doctoc generated TOC please keep comment here to allow auto update -->

---

↓ 下面内容已过时，最新下载方式请见上面内容 ↑

# 概述

Chrome(Chromium) 历史旧版本离线安装包，官方下载地址。

- 页面打不开、打开异常，可能与 Chrome 的版本有关。
- 此时你可能需要启动旧版本 Chrome 测试兼容性。

# Chrome 离线包 - 最新版（官方）

- 页面：https://www.google.com/intl/en/chrome/browser/desktop/index.html?standalone=1
- 点击`Accept and Download`会下载离线包（界面与`在线安装`类似，但确实是`离线包`）

# Chrome 离线包 - 历史版本（官方）

下载：https://commondatastorage.googleapis.com/chromium-browser-snapshots/index.html

> 上述链接来自 [Chromium 官网](https://www.chromium.org/getting-involved/download-chromium)，
> 含 Mac、Linux、Windows、Android、ChromiumOS 等

- 参考这里 https://chromium.googlesource.com/chromiumos/manifest-versions/+/master/paladin/buildspecs/ 来下载对应的离线版本包

# Chrome 离线包 - 历史版本（非官方）

主页：http://crportable.sourceforge.net/

下载：http://crportable.sourceforge.net/releases.html

使用：直接解压 exe 文件运行即可（无需安装过程）

- 优点：
  - 版本相对齐全（从 2010 年的 v9 到 最新版都有）
  - 可同时打开多个版本的 Chrome
- 缺点：
  - 非官方的下载链接（但看起来貌似可以接受）
  - 与正式 Release 版有一定区别，不过暂时没感觉出区别
  - 缺失部分版本（原因未知）

**P.S. 严重求：官方的、方便的下载 Chrome 历史版本的方法**

**欢迎补充更好的方法**