﻿<adonisControls:AdonisWindow
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:adonisUi="clr-namespace:AdonisUI;assembly=AdonisUI"
    xmlns:adonisControls="clr-namespace:AdonisUI.Controls;assembly=AdonisUI"
    xmlns:av="http://schemas.microsoft.com/expression/blend/2008" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="av"
    x:Class="XChrome.forms.Loading"
    Title="Loading"
    Width="372"
    Height="107"
    IconVisibility="Hidden"
    TitleVisibility="Hidden"
    ShrinkTitleBarWhenMaximized="False"  Loaded="AdonisWindow_Loaded"                          
    PlaceTitleBarOverContent="True"  VerticalAlignment="Center" WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    <Grid>

        <StackPanel Grid.Column="1"
            Orientation="Vertical">
            <ContentControl Focusable="False"
                    ContentTemplate="{DynamicResource {x:Static adonisUi:Templates.LoadingDots}}"
                    Foreground="{DynamicResource {x:Static adonisUi:Brushes.ForegroundBrush}}"
                    Width="32"
                    Height="24"
                    Margin="0,22,0,0"/>

            <TextBlock HorizontalAlignment="Center" Margin="0,5,0,0" x:Name="tipblock" TextWrapping="Wrap" Text="...加载中..." FontSize="16" VerticalAlignment="Top"/>
        </StackPanel>
    </Grid>
</adonisControls:AdonisWindow>
