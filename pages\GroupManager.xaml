﻿<Page x:Class="XChrome.pages.GroupManager"
xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
xmlns:local="clr-namespace:XChrome.pages"
xmlns:adonisUi="clr-namespace:AdonisUI;assembly=AdonisUI"
xmlns:adonisControls="clr-namespace:AdonisUI.Controls;assembly=AdonisUI"
xmlns:adonisExtensions="clr-namespace:AdonisUI.Extensions;assembly=AdonisUI"
xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      Loaded="Page_Loaded"
      Title="GroupManager">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="120"></RowDefinition>
           
        </Grid.RowDefinitions>
        <!--搜索-->
        <DockPanel  Grid.Row="0"  Margin="0,5,5,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                    <ColumnDefinition Width="100"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" >
                    <TextBox x:Name="sText" x:FieldModifier="private" Margin="0,0,0,0" MinWidth="300" adonisExtensions:WatermarkExtension.Watermark="分组名称..." />
                    <Button Margin="10,0,0,0" Content=" 搜索 " Click="Button_Click" Padding="12,4,12,4"  Style="{DynamicResource {x:Static adonisUi:Styles.AccentButton}}"   />
                </StackPanel>
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Stretch">
                    <Button  x:Name="add_button" Click="add_button_Click" HorizontalAlignment="Center" Height="30" VerticalAlignment="Center" Style="{DynamicResource {x:Static adonisUi:Styles.AccentButton}}">
                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                            <iconPacks:PackIconFontAwesome Kind="SquarePlusRegular"
Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                            <TextBlock VerticalAlignment="Center" Text="创建分组"/>
                        </StackPanel>
                    </Button>

                </StackPanel>
            </Grid>
            

        </DockPanel>

        <!--表格-->
        <Border Grid.Row="1" BorderBrush="{DynamicResource {x:Static adonisUi:Brushes.Layer1BackgroundBrush}}" BorderThickness="1,1,1,1" Margin="0,0,5,0" Padding="5,0,5,0">
            <DockPanel Margin="0,10,0,0">
                <!--表格-->
                <DataGrid x:Name="dataGrid" MinRowHeight="40"
                   AutoGenerateColumns="False"
                   ItemsSource="{Binding TableItems, Mode=TwoWay}"
                   CanUserAddRows="False"
                   IsReadOnly="True"
                   SelectionMode="Single"
                   SelectedCellsChanged="dataGrid_SelectedCellsChanged"
       
                    >
                    <DataGrid.Columns>
                        <!-- check 列 -->
                        <DataGridCheckBoxColumn Binding="{Binding Path=Check,
                 Mode=TwoWay,
                 UpdateSourceTrigger=PropertyChanged}" Width="50">
                            <DataGridCheckBoxColumn.ElementStyle>
                                <Style TargetType="{x:Type CheckBox}">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    <Setter Property="ClickMode" Value="Press"/>
                                </Style>
                            </DataGridCheckBoxColumn.ElementStyle>
                            <DataGridCheckBoxColumn.HeaderTemplate>
                                <DataTemplate>
                                    <CheckBox Margin="14,5,5,5" x:Name="CheckAll" Click="CheckAll_Click"  HorizontalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridCheckBoxColumn.HeaderTemplate>
                        </DataGridCheckBoxColumn>

                        <!-- ID 列 -->
                        <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="40">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                       

                        <!-- 名称列 -->
                        <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="150">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="环境数" Binding="{Binding ChromeNumber}" Width="60">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="备注" Binding="{Binding Remark}" Width="*">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!--创建时间-->
                        <DataGridTextColumn Header="创建时间" Binding="{Binding CreateDate, StringFormat='{}{0:yyyy-MM-dd&#10;HH:mm:ss}'}" Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        
                    </DataGrid.Columns>
                </DataGrid>
            </DockPanel>
        </Border>

        <!--操作-->
        <StackPanel Grid.Row="2" Margin="0,3,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="250"></ColumnDefinition>
                    <ColumnDefinition Width="200"></ColumnDefinition>
                    <ColumnDefinition Width="250"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <GroupBox Grid.Column="0" Header="基础信息" Margin="0,5,5,5">
                    <WrapPanel Orientation="Horizontal">
                        <Button x:Name="edit_button" HorizontalAlignment="Center" Margin="5" Click="edit_button_Click" VerticalAlignment="Center" IsEnabled="False">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <iconPacks:PackIconFontAwesome Kind="PenToSquareRegular"
                    Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                <TextBlock VerticalAlignment="Center" Text="编辑"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="copy_button" HorizontalAlignment="Center" Margin="5" Click="copy_button_Click" VerticalAlignment="Center" IsEnabled="False" >
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <iconPacks:PackIconFontAwesome Kind="CopyRegular"
                     Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                <TextBlock VerticalAlignment="Center" Text="复制"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="del_button" HorizontalAlignment="Center" Margin="5" Click="del_button_Click" VerticalAlignment="Center" IsEnabled="False">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <iconPacks:PackIconFontAwesome Kind="TrashCanRegular"
                    Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                <TextBlock VerticalAlignment="Center" Text="删除"/>
                            </StackPanel>
                        </Button>

                    </WrapPanel>
                </GroupBox>

            </Grid>
        </StackPanel>

      


    </Grid>
</Page>
