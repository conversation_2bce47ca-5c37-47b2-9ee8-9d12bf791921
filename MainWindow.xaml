﻿<adonisControls:AdonisWindow
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:adonisUi="clr-namespace:AdonisUI;assembly=AdonisUI"
    xmlns:adonisControls="clr-namespace:AdonisUI.Controls;assembly=AdonisUI"
    xmlns:av="http://schemas.microsoft.com/expression/blend/2008" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="av"
    x:Class="XChrome.MainWindow"
    xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
    Title="MainWindow"
    Width="1233"
    Height="665"
    IconVisibility="Hidden"
    TitleVisibility="Hidden"
    ShrinkTitleBarWhenMaximized="False"                            
    PlaceTitleBarOverContent="True" Closing="AdonisWindow_Closing" Loaded="AdonisWindow_Loaded">

    <!-- 自定义标题栏内容 -->
    <adonisControls:AdonisWindow.TitleBarContent>
        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
            
        </StackPanel>
    </adonisControls:AdonisWindow.TitleBarContent>



    <!-- 窗口主要内容 -->
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="160"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <GroupBox HorizontalContentAlignment="Center" HorizontalAlignment="Left" Width="160" Padding="0,8,0,8">
            <Grid>
                <Image HorizontalAlignment="Left" Height="28" Margin="18,10,0,0" VerticalAlignment="Top" Width="108" Source="/Resources/xchrome.png"/>
                <Button HorizontalAlignment="Center" Margin="-30,57,0,0" VerticalAlignment="Top" Padding="10,8,10,8" FontSize="14" Click="Button_Click_1" >
                    <StackPanel Orientation="Horizontal">
                        <TextBlock FontWeight="Bold"><Run Text="新建浏览器"/></TextBlock>
                        
                    </StackPanel>
                </Button>
                <Button x:Name="mCreate_btn" Click="mCreate_btn_Click"  HorizontalAlignment="Center" ToolTip="批量创建" Margin="89,57,0,0" VerticalAlignment="Top" Height="34" Padding="4,0,0,0" FontSize="14">
                    <iconPacks:PackIconFontAwesome  Kind="HeartCirclePlusSolid" Width="20" Height="15"  VerticalAlignment="Center"  Margin="0,3,4,0"/>
                </Button>

                <ListBox VerticalContentAlignment="Center" x:Name="mainListBox"  VerticalAlignment="Top" SelectionChanged="ListBox_SelectionChanged"  HorizontalAlignment="Left" Width="160" Height="280" Margin="0,120,0,0" Panel.ZIndex="100">
                    <ListBoxItem x:Name="ListBoxItem_cmanager" Tag="chrome"  Height="39" HorizontalAlignment="Center" Padding="20,9,0,4" Width="160" VerticalAlignment="Bottom">
                        <StackPanel Orientation="Horizontal">
                            <iconPacks:PackIconFontAwesome Kind="ChromeBrands" Width="20" Height="15"  VerticalAlignment="Center"  Margin="0,3,4,0"/>
                            <TextBlock Padding="5,3,5,0" FontSize="14"><Run Text="环境管理"/></TextBlock>
                        </StackPanel>
                    </ListBoxItem>
                    <ListBoxItem Height="39" Tag="group" HorizontalAlignment="Center" Padding="20,9,0,4" Width="160" VerticalAlignment="Bottom">
                        <StackPanel Orientation="Horizontal">
                            <iconPacks:PackIconFontAwesome Kind="ObjectUngroupRegular" Width="20" Height="15"  VerticalAlignment="Center"  Margin="0,3,4,0"/>
                            <TextBlock Padding="5,3,5,0" FontSize="14"><Run Text="分组管理"/></TextBlock>
                        </StackPanel>
                    </ListBoxItem>
                  
                    <ListBoxItem Height="39" Tag="script" HorizontalAlignment="Center" Padding="20,9,0,4" Width="160" VerticalAlignment="Bottom">
                        <StackPanel Orientation="Horizontal">
                            <iconPacks:PackIconFontAwesome Kind="SubscriptSolid" Width="20" Height="15"  VerticalAlignment="Center"  Margin="0,3,4,0"/>
                            <TextBlock Padding="5,3,5,0" FontSize="14"><Run Text="脚本管理"/></TextBlock>
                        </StackPanel>
                    </ListBoxItem>
                    <ListBoxItem Height="39" Tag="api" HorizontalAlignment="Center" Padding="20,9,0,4" Width="160" VerticalAlignment="Bottom">
                        <StackPanel Orientation="Horizontal">
                            <iconPacks:PackIconFontAwesome Kind="AutoprefixerBrands" Width="20" Height="15"  VerticalAlignment="Center"  Margin="0,3,4,0"/>
                            <TextBlock Padding="5,3,5,0" FontSize="14"><Run Text="API管理"/></TextBlock>
                        </StackPanel>
                    </ListBoxItem>
                    <ListBoxItem Height="39" Tag="huishou" HorizontalAlignment="Center" Padding="20,9,0,4" Width="160" VerticalAlignment="Bottom">
                        <StackPanel Orientation="Horizontal">
                            <iconPacks:PackIconFontAwesome Kind="RecycleSolid" Width="20" Height="15"  VerticalAlignment="Center"  Margin="0,3,4,0"/>
                            <TextBlock Padding="5,3,5,0" FontSize="14"><Run Text="回收站"/></TextBlock>
                        </StackPanel>
                    </ListBoxItem>
                    <Line X1="1" Y1="10" X2="120" Y2="10" Stroke="Black" StrokeThickness="0.1" />
                    <ListBoxItem Height="39" Tag="set" HorizontalAlignment="Center" Padding="20,9,0,4" Width="160" VerticalAlignment="Bottom">
                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                            <iconPacks:PackIconFontAwesome Kind="ScrewdriverWrenchSolid" Width="20" Height="15"  VerticalAlignment="Center"  Margin="0,3,4,0"/>
                            <TextBlock VerticalAlignment="Center" FontSize="14" Text="系统设置"/>
                        </StackPanel>
                    </ListBoxItem>


                </ListBox>
                <StackPanel VerticalAlignment="Bottom" Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock>浅色</TextBlock>
                    <CheckBox x:Name="SchemeCheck" Content="深色" Style="{DynamicResource {x:Static adonisUi:Styles.ToggleSwitch}}" Margin="5,0,0,20" Click="CheckBox_Click"/>
                </StackPanel>
                <StackPanel VerticalAlignment="Bottom" Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock x:Name="ver_text"></TextBlock>
                </StackPanel>
            </Grid>
        </GroupBox>

        <Frame x:Name="MainFrame_main" Grid.Column="1" NavigationUIVisibility="Hidden"  Margin="20,30,0,0" ></Frame>
        <Frame x:Name="MainFrame_other" Grid.Column="1" NavigationUIVisibility="Hidden" Visibility="Hidden" Margin="20,30,0,0" ></Frame>

    </Grid>
</adonisControls:AdonisWindow>