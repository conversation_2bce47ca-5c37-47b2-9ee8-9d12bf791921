﻿<Page x:Class="XChrome.pages.CManager"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:XChrome.pages"
      xmlns:adonisUi="clr-namespace:AdonisUI;assembly=AdonisUI"
      xmlns:adonisControls="clr-namespace:AdonisUI.Controls;assembly=AdonisUI"
      xmlns:adonisExtensions="clr-namespace:AdonisUI.Extensions;assembly=AdonisUI"
      xmlns:iconPacks="http://metro.mahapps.com/winfx/xaml/iconpacks"
      mc:Ignorable="d" 
      d:DesignHeight="450" d:DesignWidth="800"
      JournalEntry.KeepAlive="True"
      Title="CManager" Loaded="Page_Loaded">

   

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="150"></RowDefinition>
            <RowDefinition Height="50"></RowDefinition>
        </Grid.RowDefinitions>
        <!--搜索-->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,5,5,5">
            <ComboBox x:Name="groupList" x:FieldModifier="private" Width="150" SelectedIndex="0">
                
            </ComboBox>
            <TextBox Margin="10,0,0,0" x:Name="search_text" Width="300" adonisExtensions:WatermarkExtension.Watermark="收入 名称，代理，分类等..." />
            <Button Margin="10,0,0,0" Content=" 搜 索 " Padding="12,4,12,4" 
                    Style="{DynamicResource {x:Static adonisUi:Styles.AccentButton}}" Click="Button_Click_1"  />
            <Button Margin="10,0,0,0" Content=" 重置搜索 " Padding="12,4,12,4" Click="Button_Click_2"  />
        </StackPanel>
        <!--表格-->
        <Border Grid.Row="1" BorderBrush="{DynamicResource {x:Static adonisUi:Brushes.Layer1BackgroundBrush}}" BorderThickness="1,1,1,1" Margin="0,0,5,0" Padding="5,0,5,0">
            
                <!--表格-->
                <DataGrid x:Name="dataGrid" MinRowHeight="40" Margin="0,8,0,-10"
                          AutoGenerateColumns="False" 
                          ItemsSource="{Binding TableItems, Mode=TwoWay}"
                          CanUserAddRows="False"
                          IsReadOnly="True"
                          SelectionMode="Single" ScrollViewer.HorizontalScrollBarVisibility="Visible"
                          SelectedCellsChanged="dataGrid_SelectedCellsChanged"
                           >
                    <DataGrid.Columns>
                        <!-- check 列 -->
                        <DataGridCheckBoxColumn Binding="{Binding Path=Check,
                        Mode=TwoWay,
                        UpdateSourceTrigger=PropertyChanged}" Width="50">           
                            <DataGridCheckBoxColumn.ElementStyle>
                                <Style TargetType="{x:Type CheckBox}">
                                    <Setter Property="Focusable" Value="False"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    <Setter Property="ClickMode" Value="Press"/>
                                    <!-- 使用 EventSetter 添加 Click 事件 -->
                                    <EventSetter Event="Click" Handler="CheckBoxOne_Click"/>
                                </Style>
                            </DataGridCheckBoxColumn.ElementStyle>
                            <DataGridCheckBoxColumn.HeaderTemplate>
                                <DataTemplate>
                                    <CheckBox Margin="14,5,5,5" x:Name="CheckAll" Click="CheckAll_Click" IsEnabled="{Binding IsCheckEnble}"  HorizontalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridCheckBoxColumn.HeaderTemplate>
                        </DataGridCheckBoxColumn>

                        <!-- ID 列 -->
                        <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="40">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!--状态-->
                        <DataGridTemplateColumn Header="状态">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <iconPacks:PackIconFontAwesome  Width="16" Height="16" VerticalAlignment="Center" HorizontalAlignment="Center" >
                                        <iconPacks:PackIconFontAwesome.Style>
                                            <Style TargetType="{x:Type iconPacks:PackIconFontAwesome}">
                                                <!-- 默认显示停止中的图标 -->
                                                <Setter Property="Kind" Value="CircleStopSolid" />
                                                <Style.Triggers>
                                                    <!-- 当 IsRunning 为 true 时，显示启动中的图标 -->
                                                    <DataTrigger Binding="{Binding IsRunning}" Value="True">
                                                        <Setter Property="Kind" Value="PersonRunningSolid" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </iconPacks:PackIconFontAwesome.Style>
                                    </iconPacks:PackIconFontAwesome>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>


                        <!-- 名称列 -->
                        <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="150">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="分组" Binding="{Binding Group}" Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <!-- IP 列 -->
                        <DataGridTextColumn Header="IP" Binding="{Binding Ip}" Width="150">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                       

                        <!--
                        <DataGridTemplateColumn Header="标签" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    
                                    <ItemsControl ItemsSource="{Binding Tags}">
                                        
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <WrapPanel />
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                           
                                                <Border Background="#FF6BB2FF"  VerticalAlignment="Center" HorizontalAlignment="Center"
                                        CornerRadius="5"  Padding="4" Margin="2,5,2,2">
                                                    <TextBlock Text="{Binding}" TextWrapping="Wrap"  Foreground="White" FontWeight="Bold"/>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        -->
                        
            
                        <DataGridTextColumn Header="备注" Binding="{Binding Remark}" Width="*">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 日期列 -->
                        <DataGridTextColumn Header="最近打开" Binding="{Binding DoDate, StringFormat='{}{0:yyyy-MM-dd&#x000A;HH:mm:ss}'}" Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <!--创建时间-->
                        <DataGridTextColumn Header="创建时间" Binding="{Binding CreateDate, StringFormat='{}{0:yyyy-MM-dd&#x000A;HH:mm:ss}'}" Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="{x:Type TextBlock}">
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                    <DataGridTemplateColumn Header="操作" Width="50">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button x:Name="showmore_btn"  Focusable="False" PreviewMouseLeftButtonDown="showmore_btn_PreviewMouseLeftButtonDown" Style="{DynamicResource {x:Static adonisUi:Styles.AccentToolbarButton}}" HorizontalAlignment="Center" Margin="2" VerticalAlignment="Center">
                                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                        <iconPacks:PackIconFontAwesome Kind="CaretDownSolid" Width="14" Height="20"  VerticalAlignment="Center" />
                                    </StackPanel>
                                    <Button.ContextMenu>
                                        <ContextMenu Width="100">
                                            <MenuItem Header="启动" IsEnabled="{Binding IsStoped}" Click="MenuItem_Click" >
                                                <MenuItem.Icon>
                                                    <iconPacks:PackIconFontAwesome  Kind="CirclePlayRegular" Width="16" Height="14"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="停止" IsEnabled="{Binding IsRunning}" Click="MenuItem_Click_1Async">
                                                <MenuItem.Icon>
                                                    <iconPacks:PackIconFontAwesome  Kind="CircleStopSolid" Width="16" Height="14"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <Separator />
                                            <MenuItem Header="编辑" Click="MenuItem_Click_1" >
                                                <MenuItem.Icon>
                                                    <iconPacks:PackIconFontAwesome Kind="PenToSquareRegular" Width="16" Height="14"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="复制" Click="MenuItem_Click_2">
                                                <MenuItem.Icon>
                                                    <iconPacks:PackIconFontAwesome Kind="CopyRegular" Width="16" Height="14"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="删除" IsEnabled="{Binding IsStoped}" Click="MenuItem_Click_3">
                                                <MenuItem.Icon>
                                                    <iconPacks:PackIconFontAwesome Kind="TrashCanRegular" Width="16" Height="14"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <!-- 你可以继续添加其他菜单项 -->
                                        </ContextMenu>
                                    </Button.ContextMenu>
                                </Button>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>


                </DataGrid.Columns>
                </DataGrid>
            
        </Border>
        <!--操作-->
        <StackPanel Grid.Row="2" Margin="0,3,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"></ColumnDefinition>
                        <ColumnDefinition Width="500"></ColumnDefinition>
                        <ColumnDefinition Width="250"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <GroupBox Grid.Column="0" Header="基础信息" Margin="0,5,5,5">
                        <WrapPanel Orientation="Horizontal">
                            <Button x:Name="edit_btn" IsEnabled="{Binding buttonStatus.Edit}" Click="edit_btn_Click" HorizontalAlignment="Center" Margin="5,0,0,0" VerticalAlignment="Center">
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                    <iconPacks:PackIconFontAwesome Kind="PenToSquareRegular"
                          Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                    <TextBlock VerticalAlignment="Center" Text="编辑"/>
                                </StackPanel>
                            </Button>

                          <Button x:Name="copy_btn" IsEnabled="{Binding buttonStatus.Copy}" Click="copy_btn_Click" HorizontalAlignment="Center" Margin="5,3,0,0" VerticalAlignment="Center">
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                    <iconPacks:PackIconFontAwesome Kind="CopyRegular"
                           Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                    <TextBlock VerticalAlignment="Center" Text="复制"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="del_btn" IsEnabled="{Binding buttonStatus.Del}" Click="del_btn_Click" HorizontalAlignment="Center" Margin="5,3,0,0" VerticalAlignment="Center">
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                    <iconPacks:PackIconFontAwesome Kind="TrashCanRegular"
                          Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                    <TextBlock VerticalAlignment="Center" Text="删除"/>
                                </StackPanel>
                            </Button>

                       


                    </WrapPanel>
                    </GroupBox>

                    <GroupBox Grid.Column="1" Header="群控" Margin="5">
                        <WrapPanel x:Name="wppanel_pailie" Orientation="Horizontal">

                        <Button x:Name="start_btn" IsEnabled="{Binding buttonStatus.Run}" Click="start_btn_Click" HorizontalAlignment="Center"  VerticalAlignment="Center">
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                    <iconPacks:PackIconFontAwesome Kind="CirclePlayRegular"
    Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                    <TextBlock VerticalAlignment="Center" Text="运行且排序"/>
                                </StackPanel>
                            </Button>

                        <Button x:Name="start2_btn" IsEnabled="{Binding buttonStatus.Run}" Click="start_btn_Click" HorizontalAlignment="Center" Margin="5,0,0,0"  VerticalAlignment="Center">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <iconPacks:PackIconFontAwesome Kind="CirclePlayRegular"
Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                <TextBlock VerticalAlignment="Center" Text="只运行"/>
                            </StackPanel>
                        </Button>


                        <Button x:Name="stop_btn" IsEnabled="{Binding buttonStatus.Stop}" Click="stop_btn_Click" HorizontalAlignment="Center" Margin="5,0,0,0" VerticalAlignment="Center">
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                    <iconPacks:PackIconFontAwesome Kind="CircleStopSolid"
    Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                    <TextBlock VerticalAlignment="Center" Text="全部停止"/>
                                </StackPanel>
                            </Button>

                        <Button x:Name="kong_btn" IsEnabled="{Binding buttonStatus.Control}" Click="kong_btn_Click" HorizontalAlignment="Center" Margin="5,0,100,0" VerticalAlignment="Center">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <iconPacks:PackIconFontAwesome Kind="GamepadSolid"
Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                <TextBlock VerticalAlignment="Center" Text="群控开关"/>
                            </StackPanel>
                        </Button>

                        <ComboBox x:Name="screen_com" x:FieldModifier="private" Margin="0,5,0,0" Width="120" SelectedIndex="0">
                            
                            </ComboBox>

                            <ComboBox x:Name="pl_type_com" Margin="5,5,0,0" Width="80" SelectedIndex="0">
                                <ComboBoxItem Content="宫格平铺"></ComboBoxItem>
                                <ComboBoxItem Content="重叠平铺"></ComboBoxItem>
                                <ComboBoxItem Content="居中重叠"></ComboBoxItem>
                        </ComboBox>

                            <Button x:Name="pl_btn" IsEnabled="{Binding buttonStatus.Array}" Click="pl_btn_Click" Margin="5,5,0,0" HorizontalAlignment="Center"  VerticalAlignment="Center">
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                    <iconPacks:PackIconFontAwesome Kind="LayerGroupSolid" Width="16" Height="16"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                    <TextBlock VerticalAlignment="Center" Text="开始排列窗口"/>
                                </StackPanel>
                            </Button>
                            
                           


                        <WrapPanel Margin="0,5,0,0">


                                <CheckBox x:Name="pl_custom_check" Content="自定义：" />

                                <TextBlock VerticalAlignment="Center" Padding="5,0,5,0">宽</TextBlock>
                                <TextBox x:Name="pl_width_text" Text="500" Width="50"></TextBox>
                                <TextBlock  VerticalAlignment="Center" Padding="5,0,5,0">高</TextBlock>
                                <TextBox x:Name="pl_height_text" Text="500" Width="50"></TextBox>
                                <TextBlock  VerticalAlignment="Center" Padding="5,0,5,0">列数</TextBlock>
                                <TextBox x:Name="pl_wcount_text" Text="3" Width="50"></TextBox>
                            </WrapPanel>


                        <WrapPanel Width="400" Margin="0,5,0,0">
                            

                            <CheckBox x:Name="IsRatio_check" Visibility="Hidden" Click="IsRatio_check_Click" Margin="5,0,0,0" >
                                <TextBlock Text="相对位置"  />
                            </CheckBox>
                            <TextBlock Text="【?】" Visibility="Hidden" x:Name="IsRatio_check_help" Background="Transparent" MouseLeftButtonDown="IsRatio_check_help_MouseLeftButtonDown"  VerticalAlignment="Center" TextDecorations="Underline" />
                        </WrapPanel>



                       


                    </WrapPanel>
                    </GroupBox>

                <GroupBox x:Name="script_group" IsEnabled="{Binding test.X}" Grid.Column="2" Header="脚本运行" Margin="5">
                        <WrapPanel Orientation="Horizontal">
                            <ComboBox x:Name="script_sel" Width="200" SelectedIndex="0" Margin="5">
                                <ComboBoxItem Content="选中脚本"></ComboBoxItem>
                                <ComboBoxItem Content="xxx"></ComboBoxItem>
                            </ComboBox>
                        <Button x:Name="script_do_btn" IsEnabled="{Binding buttonStatus.RunScript}" Click="script_do_btn_Click" HorizontalAlignment="Center" Margin="5,0" VerticalAlignment="Center">
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                    <iconPacks:PackIconFontAwesome Kind="NodeJsBrands"
                         Width="20" Height="18"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                    <TextBlock VerticalAlignment="Center" Text="开始运行"/>
                                </StackPanel>
                            </Button>
                        </WrapPanel>
                    </GroupBox>
                </Grid>
            </StackPanel>
        <!--翻页-->
        <Border Grid.Row="3" BorderBrush="{DynamicResource {x:Static adonisUi:Brushes.Layer1BackgroundBrush}}" BorderThickness="1,1,1,1" Margin="0,1,5,5" Padding="5,0,5,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="350"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0" Margin="2,6,0,0">
                    <TextBlock Foreground="Gray">
                        关注，转发推特，是对我最大的支持：
                        <Hyperlink x:Name="gogithub_url" Click="gogithub_url_Click" >点击打开推特</Hyperlink>
                        <LineBreak/>
                        加QQ群交流：30286103
                    </TextBlock>
                    
                </StackPanel>
                <StackPanel Grid.Column="1" HorizontalAlignment="Right" Orientation="Horizontal" VerticalAlignment="Center">
                    <!-- 分页按钮区域 -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Cursor="" >
                        <Button x:Name="pre_btn" Click="pre_btn_Click" HorizontalAlignment="Center" Height="30" Margin="5" VerticalAlignment="Center">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <iconPacks:PackIconFontAwesome Kind="CircleChevronLeftSolid" Width="20" Height="15"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                                <TextBlock VerticalAlignment="Center" Text="上一页"/>
                            </StackPanel>
                        </Button>
                        <!-- 显示当前页 -->
                        <TextBox x:Name="currentPage" KeyDown="currentPage_KeyDown" Margin="10,0,0,0" Height="30" Width="50" />
                        <TextBlock x:Name="tbCurrentPage"  FontSize="14" HorizontalAlignment="Center" Margin="10,11,10,10" Text="/ 15"/>

                        <Button x:Name="next_btn" Click="next_btn_Click" HorizontalAlignment="Center" Height="30" Margin="5" VerticalAlignment="Center">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <TextBlock VerticalAlignment="Center" Text="下一页"/>
                                <iconPacks:PackIconFontAwesome Kind="CircleChevronRightSolid" Width="20" Height="15"  VerticalAlignment="Center"  Margin="0,0,4,0"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </Grid>
            


        </Border>
    </Grid>
</Page>
